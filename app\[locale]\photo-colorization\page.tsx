import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import KontextDev from "@/components/kontextdev";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/photo-colorization`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/photo-colorization`;
  }

  return {
    title: "AI Photo Colorization - Restore Old Photos",
    description: "Transform your black and white photos into vibrant colored images using advanced AI technology. Bring old memories to life with realistic colorization.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PhotoColorizationPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // 专门为照片上色功能定制的对比图片
  const compareImages = {
    originalSrc: "https://pic.kontext-dev.com/color-page.webp",
    modifiedSrc: "https://pic.kontext-dev.com/colorized-page-with-kontext-dev.webp",
    beforeText: "Black and white photo",
    afterText: "AI Colorized Photo"
  };

  // 照片上色专用的示例图片对比数据
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.kontext-dev.com/color-page.webp",
      modifiedSrc: "https://pic.kontext-dev.com/colorized-page-with-kontext-dev.webp",
      alt: "Black and white photo colorized with AI",
      beforeText: "Original black and white photo",
      afterText: "AI Colorized Result"
    },
    {
      id: 2,
      originalSrc: "https://pic.restore-old-photos.com/grandma-photo-damaged-before.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/grandma-photo-restored-after.png",
      alt: "Vintage family photo colorized",
      beforeText: "Vintage black and white portrait",
      afterText: "Colorized with Natural Tones"
    },
    {
      id: 3,
      originalSrc: "https://pic.restore-old-photos.com/mother-daughter-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/mother-daughter-photo-restored-after-repair.png",
      alt: "Mother and daughter photo colorized",
      beforeText: "Classic family moment",
      afterText: "Brought to Life with Color"
    },
    {
      id: 4,
      originalSrc: "https://pic.restore-old-photos.com/sister-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/sister-photo-ai-restored.png",
      alt: "Portrait colorization example",
      beforeText: "Monochrome portrait",
      afterText: "Realistic Color Enhancement"
    },
    {
      id: 5,
      originalSrc: "https://pic.restore-old-photos.com/woman-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/woman-photo-ai-restored.jpg",
      alt: "Vintage woman portrait colorized",
      beforeText: "Historical black and white",
      afterText: "Vibrant Color Restoration"
    },
    {
      id: 6,
      originalSrc: "https://pic.restore-old-photos.com/family-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/family-photo-ai-restored.jpg",
      alt: "Family group photo colorization",
      beforeText: "Family group portrait",
      afterText: "Colorful Memory Revival"
    }
  ];

  // 为照片上色功能定制的Hero内容
  const colorizationHero = {
    ...page.hero,
    title: "Transform Black & White Photos into Vibrant Colored Images",
    highlight_text: "AI Photo Colorization",
    description: "Bring your old black and white photos to life with our advanced AI colorization technology.<br/>Experience realistic colors that preserve the authenticity of your precious memories.",
    buttons: [
      {
        title: "Start Colorizing",
        icon: "RiPaletteFill",
        url: "#colorization-tool",
        target: "_self",
        variant: "default"
      },
      {
        title: "View Examples",
        icon: "RiImageLine",
        url: "#examples",
        target: "_self",
        variant: "outline"
      }
    ]
  };

  return (
    <>
      {/* Hero Section with Photo Colorization Focus */}
      {colorizationHero && <HeroWithCompare 
        hero={colorizationHero} 
        compareImages={compareImages}
      />}
      
      {/* Main Colorization Tool - 复用首页的功能区 */}
      <div id="colorization-tool" className="py-12">
        <div className="container">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold tracking-tight mb-4">AI Photo Colorization Tool</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Upload your black and white photo and watch our AI transform it into a beautifully colored image with realistic tones and natural-looking results.
            </p>
          </div>
        </div>
        <KontextDev 
          title="AI Photo Colorization"
          showPromptInput={false}
          showTranslation={false}
          showQuickActions={false}
          showImageSettings={true}
          showWatermarkToggle={true}
          defaultPrompt="Transform this black and white photograph into a vibrant colored image. Apply realistic and natural colors to skin tones, clothing, backgrounds, and all elements in the photo. Ensure the colorization looks authentic and historically appropriate while maintaining the original photo's quality and details."
          generateButtonText="Colorize Photo"
          resultTitle="Colorized Photo Result"
          disableTranslation={true}
        />
      </div>
      
      {/* Examples Gallery */}
      <div id="examples">
        <ImageCompareGallery 
          title="AI Photo Colorization - Before and After Examples"
          description="See the amazing transformation of black and white photos into vibrant colored images. Our AI colorization technology brings old memories to life with realistic colors and natural tones."
          compareGroups={compareGroups} 
        />
      </div>

      {/* Reuse existing page sections with colorization context */}
      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      
      {/* Pricing Section */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Colorize Your Photos?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Explore our pricing plans and bring your black and white memories to life
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Pricing Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>
      
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
