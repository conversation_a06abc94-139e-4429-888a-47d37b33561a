import { LandingPage } from "@/types/pages/landing";

export async function getPhotoColorizationPage(locale: string): Promise<LandingPage> {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/photo-colorization/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load photo-colorization ${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/photo-colorization/en.json").then(
      (module) => module.default as LandingPage
    );
  }
}
